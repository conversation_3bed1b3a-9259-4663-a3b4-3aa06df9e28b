# Enhanced Cable Selection Feature Test Guide

## Overview
The enhanced cable selection feature creates a clear visual distinction on the map when users click on cable results in the search sidebar. This helps users identify which specific cable they have selected by creating a visual hierarchy where the selected cable stands out prominently while all others fade into the background.

## Key Features Implemented

### 1. Visual Hierarchy System
- **Selected Cable**: Bright green color (#00FF00), thick line (weight: 7), fully opaque, brought to front layer
- **Non-Selected Cables**: Original colors but faded to 25% opacity, reduced weight (1.2), pushed to back layer
- **Search Results**: Selected items show green styling with green circle icon (🟢)

### 2. Preserved Functionality
- **Cable Information Display**: All existing cable information and name labels continue to work
- **Map Centering**: Selected cables are automatically centered on the map
- **Popup Information**: Cable popups and details remain fully functional
- **Search Integration**: Works seamlessly with existing search functionality

### 3. User Interactions
- **Click on search result**: Selects/deselects cable with visual hierarchy on map
- **Click same result again**: Deselects cable and returns all cables to normal
- **Click different result**: Switches selection to new cable
- **Click empty map area**: Clears selection and returns to normal view
- **Clear search**: Automatically clears selection along with search results

## Testing Steps

### Test 1: Basic Cable Selection from Search Results
1. Open the submarine cable map
2. Search for cables between two countries (e.g., "South Africa" to "United Kingdom")
3. Click on one of the cable results in the sidebar
4. **Expected Results**:
   - The corresponding cable line on the map becomes bright green and thick
   - All other cable lines become faded (25% opacity) creating a ghosted background
   - The clicked search result shows green styling with green circle icon (🟢)
   - The selected cable is easy to trace across the map
   - Cable information and name label appear as before
   - Map centers on the selected cable

### Test 2: Cable Deselection
1. With a cable selected (from Test 1)
2. Click on the same cable result again
3. **Expected Results**:
   - All cable lines return to normal appearance
   - The search result loses green styling and green circle icon
   - All cables are equally visible again
   - Cable information remains visible

### Test 3: Switching Between Cable Selections
1. Search for cables and select one cable from search results
2. Click on a different cable result in the search list
3. **Expected Results**:
   - Previous selection is cleared automatically
   - New cable becomes highlighted with bright green
   - Only the newly selected cable is prominent
   - Search results update to show new selection
   - Map centers on new cable

### Test 4: Map Click Deselection
1. Select a cable from search results
2. Click on an empty area of the map (not on a cable or landing point)
3. **Expected Results**:
   - Cable selection is cleared
   - All cables return to normal appearance
   - Search result styling is cleared
   - Search results remain visible

### Test 5: Search Clear Behavior
1. Select a cable from search results
2. Clear the search using the "Clear Search" button
3. **Expected Results**:
   - Selection is cleared along with search results
   - Map returns to normal view with all cables visible
   - All visual hierarchy effects are removed

## Visual Indicators

### On the Map:
- **Selected Cable**: Bright green (#00FF00), weight: 7, opacity: 1.0, brought to front
- **Non-selected Cables**: Original colors but weight: 1.2, opacity: 0.25 (ghosted effect), pushed to back
- **Clear Visual Hierarchy**: Selected cable dominates the view while others fade to background

### In Search Results:
- **Selected Result**: Green background (#f0fff0), green text, green circle icon (🟢)
- **Non-selected Results**: Normal styling

## Integration Benefits
1. **Preserves All Existing Features**: Cable information, popups, centering, and labels work exactly as before
2. **Enhanced User Experience**: Clear visual focus makes cable tracing effortless
3. **Intuitive Interaction**: Click to select, click again to deselect, or click map to clear
4. **Seamless Search Integration**: Works perfectly with existing search functionality
5. **Professional Appearance**: Creates modern mapping application experience

## Browser Console Logs
The implementation includes detailed console logging:
- "🎯 Applying cable selection for: [cable-id]"
- "✨ Selected cable highlighted with visual emphasis: [cable-name]"
- "👻 Non-selected cable faded to background: [cable-name]"
- "🔄 Clearing cable selection"
- "🗺️ Cable selection cleared by map click"
- "🎯 Selected cable: [cable-name]"
- "🔄 Deselected cable: [cable-name]"

## Key Benefits
1. **Clear Visual Focus**: Selected cables stand out dramatically against faded background
2. **Easy Route Tracing**: Users can effortlessly follow specific cable paths
3. **Reduced Visual Clutter**: Non-selected cables fade away, reducing distraction
4. **Preserved Functionality**: All existing features continue to work seamlessly
5. **Modern UX**: Professional mapping application experience with visual hierarchy
