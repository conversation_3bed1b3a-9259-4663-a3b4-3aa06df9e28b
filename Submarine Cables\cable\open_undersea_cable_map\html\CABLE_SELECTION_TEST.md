# Cable Selection Feature Test Guide

## Overview
The cable selection feature allows users to visually highlight specific submarine cable lines on the map while de-emphasizing others when clicking on search results. This creates a clear visual focus on the selected cable route.

## Features Implemented

### 1. Cable Selection State Management
- `selectedCableId`: Tracks currently selected cable
- `isSelectionActive`: Tracks if selection mode is active

### 2. Visual Styling
- **Selected Cable on Map**: Bright orange-red (#FF6B35), very thick line (weight: 8), fully opaque
- **Non-selected Cables on Map**: Faded colors, very thin lines (weight: 1), 25% opacity
- **Search Results**: Selected items show orange styling with target icon (🎯)

### 3. User Interactions
- **Click on search result**: Selects/deselects cable with map highlighting
- **Click on map**: Deselects currently selected cable
- **Search clear**: Automatically clears selection

## Testing Steps

### Test 1: Basic Cable Selection and Map Highlighting
1. Open the submarine cable map
2. Search for cables between two countries (e.g., "South Africa" to "United Kingdom")
3. Click on one of the cable results in the sidebar
4. **Expected Results**:
   - The corresponding cable line on the map becomes bright orange-red and very thick
   - All other cable lines on the map become very faded (25% opacity) and thin
   - The clicked search result shows orange styling with target icon (🎯)
   - The selected cable is easy to trace across the map

### Test 2: Cable Deselection
1. With a cable selected (from Test 1)
2. Click on the same cable result again
3. **Expected Results**:
   - All cable lines on the map return to normal appearance
   - The search result loses orange styling and target icon
   - All cables are equally visible again

### Test 3: Map Click Deselection
1. Select a cable (from Test 1)
2. Click on an empty area of the map (not on a cable or landing point)
3. **Expected Results**:
   - Cable selection is cleared
   - All cables return to normal appearance
   - Search result styling is cleared

### Test 4: Switching Between Cable Selections
1. Search for cables and select one cable
2. Click on a different cable result in the search list
3. **Expected Results**:
   - Previous selection is cleared
   - New cable becomes highlighted on the map
   - Only the newly selected cable is prominent
   - Search results update to show new selection

### Test 5: Search Clear Behavior
1. Select a cable
2. Clear the search using the "Clear Search" button
3. **Expected Results**:
   - Selection is cleared along with search results
   - Map returns to normal view with all cables visible

## Visual Indicators

### On the Map:
- **Selected Cable**: Bright orange-red (#FF6B35), weight: 8, opacity: 1.0
- **Non-selected Cables**: Original colors but weight: 1, opacity: 0.25 (very faded)
- Selected cable is brought to front for maximum visibility

### In Search Results:
- **Selected Result**: Orange background (#fff5f2), orange text, target icon (🎯)
- **Non-selected Results**: Normal styling

## Browser Console Logs
The implementation includes detailed console logging:
- "🎯 Applying cable selection for: [cable-id]"
- "✨ Selected cable highlighted: [cable-name]"
- "🔸 Non-selected cable faded: [cable-name]"
- "🔄 Clearing cable selection"
- "🗺️ Cable selection cleared by map click"
- "🎯 Selected cable: [cable-name]"
- "🔄 Deselected cable: [cable-name]"

## Key Benefits
1. **Easy Cable Tracing**: Selected cables stand out dramatically against faded background
2. **Route Visualization**: Users can easily follow the path of a specific cable
3. **Reduced Visual Clutter**: Non-selected cables fade away, reducing distraction
4. **Intuitive Interaction**: Click to select, click again to deselect, or click map to clear
5. **Seamless Integration**: Works perfectly with existing search functionality
