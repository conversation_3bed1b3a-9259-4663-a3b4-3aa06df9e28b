# Cable Selection Feature Test Guide

## Overview
The cable selection feature has been implemented to allow users to visually highlight specific cables while de-emphasizing others when clicking on search results.

## Features Implemented

### 1. Cable Selection State Management
- `selectedCableId`: Tracks currently selected cable
- `isSelectionActive`: Tracks if selection mode is active
- `selectedCableStyles`: Stores styles for selected cables

### 2. Visual Styling
- **Selected Cable**: Bright blue (#0066FF), thick line (weight: 6), fully opaque
- **Non-selected Cables**: Faded colors, thinner lines (weight: 1.5), 40% opacity
- **Search Results**: Selected items show blue styling with pin icon (📍)

### 3. User Interactions
- **Click on search result**: Selects/deselects cable
- **Click on map**: Deselects currently selected cable
- **Search clear**: Automatically clears selection

## Testing Steps

### Test 1: Basic Cable Selection
1. Open the submarine cable map
2. Search for cables between two countries (e.g., "South Africa" to "United Kingdom")
3. Click on one of the cable results
4. **Expected**: Cable should be highlighted in bright blue, others faded
5. **Expected**: Search result should show blue styling with pin icon

### Test 2: Cable Deselection
1. With a cable selected (from Test 1)
2. Click on the same cable result again
3. **Expected**: All cables return to normal appearance
4. **Expected**: Search result loses blue styling and pin icon

### Test 3: Map Click Deselection
1. Select a cable (from Test 1)
2. Click on an empty area of the map
3. **Expected**: Cable selection is cleared, all cables return to normal

### Test 4: Search Clear Behavior
1. Select a cable
2. Clear the search using the "Clear Search" button
3. **Expected**: Selection is cleared along with search results

### Test 5: Multiple Cable Selection
1. Search for cables
2. Select one cable
3. Click on a different cable result
4. **Expected**: Previous selection is cleared, new cable is selected

## Visual Indicators
- Selected cables appear in bright blue with thick lines
- Non-selected cables appear faded (40% opacity)
- Selected search results have blue background and pin icon
- Hint text appears: "💡 Click on a cable to highlight it on the map. Click again to deselect."

## Browser Console Logs
The implementation includes console logging for debugging:
- "Applying cable selection for: [cable-id]"
- "Selected cable styled: [cable-name]"
- "Non-selected cable faded: [cable-name]"
- "Clearing cable selection"
- "Cable selection cleared by map click"
