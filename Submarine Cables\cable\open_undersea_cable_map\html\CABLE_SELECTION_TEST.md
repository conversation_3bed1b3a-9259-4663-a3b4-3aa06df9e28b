# Cable Selection Feature Test Guide

## Overview
The cable selection feature allows users to visually highlight specific submarine cable lines on the map while de-emphasizing others when clicking on search results. This creates a clear visual focus on the selected cable route.

## Features Implemented

### 1. Cable Selection State Management
- `selectedCableId`: Tracks currently selected cable
- `isSelectionActive`: Tracks if selection mode is active

### 2. Visual Styling
- **Selected Cable on Map**: Bright orange-red (#FF6B35), very thick line (weight: 8), fully opaque
- **Non-selected Cables on Map**: Faded colors, very thin lines (weight: 1), 25% opacity
- **Search Results**: Selected items show orange styling with target icon (🎯)

### 3. User Interactions
- **Click on cable lines on the map**: Directly select/deselect cables by clicking on them
- **Click on search result**: Also selects/deselects cable with map highlighting
- **Click on empty map area**: Deselects currently selected cable
- **Search clear**: Automatically clears selection

## Testing Steps

### Test 1: Direct Cable Selection on Map
1. Open the submarine cable map
2. **Click directly on any cable line on the map**
3. **Expected Results**:
   - The clicked cable line becomes bright orange-red and very thick (weight: 8)
   - All other cable lines on the map become very faded (25% opacity) and thin (weight: 1)
   - The selected cable is easy to trace across the map
   - A popup appears showing cable information

### Test 2: Cable Selection from Search Results
1. Search for cables between two countries (e.g., "South Africa" to "United Kingdom")
2. Click on one of the cable results in the sidebar
3. **Expected Results**:
   - The corresponding cable line on the map becomes bright orange-red and very thick
   - All other cable lines on the map become very faded (25% opacity) and thin
   - The clicked search result shows orange styling with target icon (🎯)
   - The selected cable is easy to trace across the map

### Test 3: Cable Deselection by Clicking Same Cable
1. With a cable selected (from Test 1 or 2)
2. **Click on the same cable line on the map again** OR click the same search result again
3. **Expected Results**:
   - All cable lines on the map return to normal appearance
   - The search result loses orange styling and target icon (if applicable)
   - All cables are equally visible again

### Test 4: Map Click Deselection
1. Select a cable (from Test 1 or 2)
2. Click on an empty area of the map (not on a cable or landing point)
3. **Expected Results**:
   - Cable selection is cleared
   - All cables return to normal appearance
   - Search result styling is cleared

### Test 5: Switching Between Cable Selections on Map
1. **Click on one cable line on the map** to select it
2. **Click on a different cable line on the map**
3. **Expected Results**:
   - Previous selection is cleared automatically
   - New cable becomes highlighted on the map
   - Only the newly selected cable is prominent

### Test 6: Switching Between Cable Selections via Search
1. Search for cables and select one cable from search results
2. Click on a different cable result in the search list
3. **Expected Results**:
   - Previous selection is cleared
   - New cable becomes highlighted on the map
   - Only the newly selected cable is prominent
   - Search results update to show new selection

### Test 7: Search Clear Behavior
1. Select a cable (either from map or search results)
2. Clear the search using the "Clear Search" button
3. **Expected Results**:
   - Selection is cleared along with search results
   - Map returns to normal view with all cables visible

## Visual Indicators

### On the Map:
- **Selected Cable**: Bright orange-red (#FF6B35), weight: 8, opacity: 1.0
- **Non-selected Cables**: Original colors but weight: 1, opacity: 0.25 (very faded)
- Selected cable is brought to front for maximum visibility

### In Search Results:
- **Selected Result**: Orange background (#fff5f2), orange text, target icon (🎯)
- **Non-selected Results**: Normal styling

## Browser Console Logs
The implementation includes detailed console logging:
- "🎯 Applying cable selection for: [cable-id]"
- "✨ Selected cable highlighted: [cable-name]"
- "🔸 Non-selected cable faded: [cable-name]"
- "🔄 Clearing cable selection"
- "🗺️ Cable selection cleared by map click"
- "🎯 Selected cable: [cable-name]"
- "🔄 Deselected cable: [cable-name]"

## Key Benefits
1. **Easy Cable Tracing**: Selected cables stand out dramatically against faded background
2. **Route Visualization**: Users can easily follow the path of a specific cable
3. **Reduced Visual Clutter**: Non-selected cables fade away, reducing distraction
4. **Intuitive Interaction**: Click to select, click again to deselect, or click map to clear
5. **Seamless Integration**: Works perfectly with existing search functionality
